import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";
import { Progress } from "~/components/ui/progress";
import { Button } from "~/components/ui/button";
import { Too<PERSON><PERSON>, Tooltip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "~/components/ui/tooltip";
import { AlertCircle, Info, Wrench, CheckCircle2, XCircle } from "lucide-react";

type MaintenanceStatus = 'optimal' | 'warning' | 'critical' | 'maintenance_required';

interface MaintenanceIssue {
  id: string;
  component: string;
  description: string;
  severity: 'low' | 'medium' | 'high';
  detectedAt: string;
  estimatedRepairTime?: string;
  recommendedAction: string;
}

interface PredictiveMaintenancePanelProps {
  deviceId: string;
  status: MaintenanceStatus;
  healthScore: number; // 0-100 scale
  nextMaintenance: string | null; // ISO date string
  remainingUsefulLife: number; // in days
  lastMaintenanceDate: string | null; // ISO date string
  maintenanceInterval: number; // in days
  issues: MaintenanceIssue[];
  lastUpdated: string; // ISO date string
  onScheduleMaintenance: () => void;
  onAcknowledgeIssue: (issueId: string) => void;
}

const getStatusVariant = (status: MaintenanceStatus) => {
  switch (status) {
    case 'optimal':
      return { bg: 'bg-green-100', text: 'text-green-800', icon: CheckCircle2 };
    case 'warning':
      return { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: AlertCircle };
    case 'critical':
      return { bg: 'bg-red-100', text: 'text-red-800', icon: XCircle };
    case 'maintenance_required':
      return { bg: 'bg-blue-100', text: 'text-blue-800', icon: Wrench };
    default:
      return { bg: 'bg-gray-100', text: 'text-gray-800', icon: Info };
  }
};

export function PredictiveMaintenancePanel({
  deviceId,
  status,
  healthScore,
  nextMaintenance,
  remainingUsefulLife,
  lastMaintenanceDate,
  maintenanceInterval,
  issues,
  lastUpdated,
  onScheduleMaintenance,
  onAcknowledgeIssue,
}: PredictiveMaintenancePanelProps) {
  const StatusIcon = getStatusVariant(status).icon;
  const statusText = status.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  const daysUntilMaintenance = nextMaintenance
    ? Math.max(0, Math.ceil((new Date(nextMaintenance).getTime() - Date.now()) / (1000 * 60 * 60 * 24)))
    : maintenanceInterval;

  const lastMaintenanceDays = lastMaintenanceDate
    ? Math.floor((Date.now() - new Date(lastMaintenanceDate).getTime()) / (1000 * 60 * 60 * 24))
    : null;

  const maintenanceProgress = lastMaintenanceDate
    ? Math.min(100, Math.max(0, ((maintenanceInterval - daysUntilMaintenance) / maintenanceInterval) * 100))
    : 100;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Predictive Maintenance</h2>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Maintenance Status</CardTitle>
            <CardDescription>Current condition and maintenance schedule</CardDescription>
          </CardHeader>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Health Score</CardTitle>
                <div className={`h-2 w-2 rounded-full ${
                  healthScore >= 80 ? 'bg-green-500' :
                  healthScore >= 50 ? 'bg-yellow-500' : 'bg-red-500'
                }`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{Math.round(healthScore)}%</div>
                <p className="text-xs text-muted-foreground">
                  {healthScore >= 80 ? 'Optimal' : healthScore >= 50 ? 'Warning' : 'Critical'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
                <div className={`${getStatusVariant(status).bg} p-1 rounded-full`}>
                  <StatusIcon className="h-3 w-3 text-white" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{statusText}</div>
                <p className="text-xs text-muted-foreground">
                  {issues.length} active issue{issues.length !== 1 ? 's' : ''}
                </p>
              </CardContent>
            </Card>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Next Maintenance</span>
                <span className="font-medium">
                  {daysUntilMaintenance > 0
                    ? `In ~${daysUntilMaintenance} days`
                    : 'Overdue'}
                </span>
              </div>
              <Progress
                value={maintenanceProgress}
                className={`h-2 ${
                  maintenanceProgress > 90 ? 'bg-red-500' :
                  maintenanceProgress > 70 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>Last: {lastMaintenanceDate ? new Date(lastMaintenanceDate).toLocaleDateString() : 'Never'}</span>
                <span>Interval: {maintenanceInterval} days</span>
              </div>
            </div>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Remaining Life</CardTitle>
                <Wrench className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.ceil(remainingUsefulLife / 30)} months
                </div>
                <p className="text-xs text-muted-foreground">
                  ~{remainingUsefulLife} days
                </p>
              </CardContent>
            </Card>

            <div className="text-xs text-muted-foreground text-right col-span-full">
              Last updated: {new Date(lastUpdated).toLocaleString()}
            </div>

            <div className="pt-2 col-span-full">
              <Button
                variant="outline"
                className="w-full"
                onClick={onScheduleMaintenance}
              >
                <Wrench className="h-4 w-4 mr-2" />
                Schedule Maintenance
              </Button>
            </div>
          </div>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Remaining Useful Life</CardTitle>
            <CardDescription>Estimated time until component replacement</CardDescription>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center h-full">
            <div className="relative w-48 h-48">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <circle
                  className="text-gray-200"
                  strokeWidth="10"
                  stroke="currentColor"
                  fill="transparent"
                  r="40"
                  cx="50"
                  cy="50"
                />
                <circle
                  className={`${
                    remainingUsefulLife > 180 ? 'text-green-500' :
                    remainingUsefulLife > 90 ? 'text-yellow-500' : 'text-red-500'
                  }`}
                  strokeWidth="10"
                  strokeLinecap="round"
                  stroke="currentColor"
                  fill="transparent"
                  r="40"
                  cx="50"
                  cy="50"
                  strokeDasharray={`${(remainingUsefulLife / 365) * 283} 283`}
                  transform="rotate(-90 50 50)"
                />
              </svg>
              <div className="absolute inset-0 flex flex-col items-center justify-center">
                <span className="text-3xl font-bold">
                  {Math.ceil(remainingUsefulLife / 30)}
                </span>
                <span className="text-sm text-gray-500">Months</span>
                <span className="text-xs text-gray-400">
                  ~{remainingUsefulLife} days
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {issues.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Detected Issues</CardTitle>
            <CardDescription>Potential problems requiring attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {issues.map((issue) => (
                <Alert
                  key={issue.id}
                  variant={issue.severity === 'high' ? 'destructive' : 'default'}
                  className="flex items-start"
                >
                  <div className="flex-shrink-0 mt-1">
                    {issue.severity === 'high' ? (
                      <XCircle className="h-5 w-5" />
                    ) : issue.severity === 'medium' ? (
                      <AlertCircle className="h-5 w-5" />
                    ) : (
                      <Info className="h-5 w-5" />
                    )}
                  </div>
                  <div className="ml-4 flex-1">
                    <AlertTitle className="flex justify-between">
                      <span>{issue.component}</span>
                      <span className="text-xs font-normal text-gray-500">
                        {new Date(issue.detectedAt).toLocaleDateString()}
                      </span>
                    </AlertTitle>
                    <AlertDescription className="mt-1">
                      <p>{issue.description}</p>
                      <div className="mt-2 flex items-center justify-between">
                        <div className="text-sm">
                          <span className="font-medium">Recommended:</span> {issue.recommendedAction}
                        </div>
                        <div className="flex space-x-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => onAcknowledgeIssue(issue.id)}
                                >
                                  Acknowledge
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Mark this issue as reviewed</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </div>
                    </AlertDescription>
                  </div>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
